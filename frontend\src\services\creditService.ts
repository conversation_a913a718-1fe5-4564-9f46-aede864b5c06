import api, { endpoints } from './api';

export interface CreditPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  formatted_price: string;
  credit_amount: number;
  price_per_credit: number;
  features: string[];
}

export interface CreditTransaction {
  id: number;
  type: string;
  credit_amount: number;
  amount_paid: number | null;
  formatted_amount_paid: string | null;
  payment_method: string | null;
  payment_status: string;
  description: string;
  package_name: string | null;
  is_credit: boolean;
  is_debit: boolean;
  processed_at: string | null;
  created_at: string;
}

export interface CreditBalance {
  credit_balance: number;
  user_id: number;
}

export interface CreditStatistics {
  current_balance: number;
  total_purchased: number;
  total_used: number;
  total_spent: number;
  recent_transactions: Array<{
    id: number;
    type: string;
    credit_amount: number;
    description: string;
    created_at: string;
  }>;
}

export interface PaymentResponse {
  success: boolean;
  payment_url?: string;
  bill_id?: string;
  transaction_id?: number;
  error?: string;
}

export interface PaymentStatus {
  transaction_id: number;
  payment_status: string;
  credit_amount: number;
  amount_paid: number;
  processed_at: string | null;
}

export interface PaymentConfig {
  billplz_enabled: boolean;
  billplz_configured: boolean;
}

class CreditService {
  // Get user's credit balance
  async getBalance(): Promise<CreditBalance> {
    const response = await api.get('/credit/balance');
    return response.data;
  }

  // Get available credit packages
  async getPackages(): Promise<CreditPackage[]> {
    const response = await api.get('/credit/packages');
    return response.data.packages;
  }

  // Get user's credit transaction history
  async getTransactions(page: number = 1): Promise<{
    transactions: {
      data: CreditTransaction[];
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
    };
  }> {
    const response = await api.get(`/credit/transactions?page=${page}`);
    return response.data;
  }

  // Get credit statistics
  async getStatistics(): Promise<CreditStatistics> {
    const response = await api.get('/credit/statistics');
    return response.data;
  }

  // Create payment for credit package
  async createPayment(packageId: number, redirectUrl?: string): Promise<PaymentResponse> {
    const response = await api.post('/payment/create', {
      package_id: packageId,
      redirect_url: redirectUrl,
    });
    return response.data;
  }

  // Check payment status
  async checkPaymentStatus(transactionId: number): Promise<PaymentStatus> {
    const response = await api.get('/payment/status', {
      params: { transaction_id: transactionId },
    });
    return response.data;
  }

  // Get payment configuration
  async getPaymentConfig(): Promise<PaymentConfig> {
    const response = await api.get('/payment/config');
    return response.data;
  }

  // Format credit amount for display
  formatCredits(amount: number | null | undefined): string {
    const safeAmount = amount ?? 0;
    return `${safeAmount.toLocaleString()} credits`;
  }

  // Format currency for display
  formatCurrency(amount: number | null | undefined): string {
    const safeAmount = amount ?? 0;
    return `RM ${safeAmount.toFixed(2)}`;
  }

  // Get transaction type color
  getTransactionTypeColor(type: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
    switch (type) {
      case 'purchase':
        return 'success';
      case 'usage':
        return 'warning';
      case 'refund':
        return 'error';
      case 'bonus':
        return 'info';
      default:
        return 'default';
    }
  }

  // Get payment status color
  getPaymentStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  }
}

export default new CreditService();
